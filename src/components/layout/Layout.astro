---
import "../../styles/global.css";
import "../../styles/header-search.css";
import "../../styles/hamburger-menu.css";
import "../../styles/search-overlay.css";
import "../../styles/ai-search-hero.css";
import "../../styles/expandable-header-search.css";
import "../../styles/copilot.css";
import "../../styles/ai-search-mobile.css";
import "../../styles/hotel-modal.css";
import "../../styles/destination-modal.css";
import "../../styles/ai-help-modal.css";
import "../../styles/makemytrip-room-card.css";
import { ThemeProvider } from "../../contexts/ThemeContext";
import Header from "./Header.astro";
import Footer from "./Footer.astro";
import CookieConsent from "../cookie/CookieConsent";
import AppProviders from "./AppProviders";

interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
  canonicalUrl?: string;
  ogType?: string;
}

const {
  title = "Perfect Piste - Luxury Ski Holidays",
  description = "Experience the ultimate luxury ski holidays with Perfect Piste. Exclusive destinations, personalized service, and unforgettable experiences.",
  ogImage = "/og.png",
  canonicalUrl = Astro.url.toString(),
  ogType = "website",
} = Astro.props;
---

<!doctype html>
<html lang="en" class="theme-default">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="generator" content={Astro.generator} />
    <meta name="description" content={description} />
    <link rel="canonical" href={canonicalUrl} />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={ogType} />
    <meta property="og:url" content={canonicalUrl} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={ogImage} />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="Perfect Piste" />

    <!-- X (Twitter) Cards -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={ogImage} />
    <meta name="twitter:image:alt" content={title} />

    <title>{title}</title>

    <!-- Flinkk Converse Chat Widget -->
    <script
      is:inline
      src="https://qa.converse.flinkk.io/sdk.js"
      data-inbox-id="cm9zb5vdc0001cygy0nupi9hg"></script>
  </head>
  <body>
    <ThemeProvider client:load>
      <AppProviders client:load>
        <div class="flex flex-col min-h-[80vh]">
          <Header />
          <slot />
          <Footer />
          <CookieConsent client:load />
        </div>
      </AppProviders>
    </ThemeProvider>
  </body>
</html>
